import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import 'vxe-table/lib/style.css'
import VXETable from 'vxe-table'

import App from './App.vue'
import router from './router'

const app = createApp(App)

function useTable(app) {
  app.use(VXETable)
}

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(useTable)
app.use(ElementPlus, {
  locale: zhCn,
})

app.mount('#app')
