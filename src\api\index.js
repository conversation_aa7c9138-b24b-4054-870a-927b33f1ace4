import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://172.16.193.195:8808',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.url, config.params)
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('接收响应:', response.config.url, response.data)
    // 如果接口返回格式是 {code: 0, message: 'OK', data: {...}}
    if (response.data && response.data.code === 0) {
      return response.data.data
    }
    return response.data
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// API 接口定义
export const deviceAPI = {
  // 获取设备列表
  getDeviceList(type) {
    return api.get('/api/pump/device/list', {
      params: { type }
    })
  }
}

export const faultAPI = {
  // 获取故障列表
  getFaultList(params) {
    return api.get('/api/pump/fault/list', {
      params: {
        h_sitename: params.siteName,
        h_devcode: params.deviceCode,
        H_operator: params.operator,
        startTime: params.startTime,
        endTime: params.endTime,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        orderBy: params.orderBy
      }
    })
  }
}

export const operateAPI = {
  // 获取操作列表
  getOperateList(params) {
    return api.get('/api/pump/operate/list', {
      params: {
        h_sitename: params.siteName,
        h_devcode: params.deviceCode,
        H_operator: params.operator,
        startTime: params.startTime,
        endTime: params.endTime,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        orderBy: params.orderBy
      }
    })
  }
}

export const pumpAPI = {
  // 获取水泵运行时间列表
  getPumpList(params) {
    return api.get('/api/pump/runtime/list', {
      params: {
        h_devCode: params.deviceCode,
        startTime: params.startTime,
        endTime: params.endTime
      }
    })
  }
}

export const waterAPI = {
  // 获取水位列表
  getWaterList(params) {
    return api.get('/api/pump/water/list', {
      params: {
        h_sitename: params.siteName,
        h_devcode: params.deviceCode,
        startTime: params.startTime,
        endTime: params.endTime,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
        orderBy: params.orderBy
      }
    })
  }
}

export default api
